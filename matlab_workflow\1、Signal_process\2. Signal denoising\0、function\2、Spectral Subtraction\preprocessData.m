function [processedColumn2, processedColumn3] = preprocessData(column2, column3)
%PREPROCESSDATA 双通道信号预处理函数
%   对双通道传感器数据进行标准化预处理，包括归一化、去直流分量和去趋势处理。
%   该函数为后续的信号降噪和分析提供标准化的输入数据。
%
%   语法:
%   [processedColumn2, processedColumn3] = preprocessData(column2, column3)
%
%   输入参数:
%   column2 - 第二通道原始数据 (数值向量，通常为ADC采样值)
%   column3 - 第三通道原始数据 (数值向量，通常为ADC采样值)
%
%   输出参数:
%   processedColumn2 - 预处理后的第二通道数据 (归一化数值向量，范围约[-1,1])
%   processedColumn3 - 预处理后的第三通道数据 (归一化数值向量，范围约[-1,1])
%
%   处理步骤:
%   1. 归一化处理: 将ADC原始数据除以8192进行归一化
%   2. 去直流分量: 减去信号均值，消除直流偏置
%   3. 去趋势处理: 使用线性去趋势，消除信号中的线性趋势
%
%   技术参数:
%   - 归一化因子: 8192 (对应14位ADC的一半量程)
%   - 去趋势方法: 线性去趋势 (MATLAB detrend函数)
%   - 适用信号: 双通道传感器数据，特别是肠鸣音信号
%
%   示例:
%   % 基本用法
%   rawData2 = [1000, 2000, 1500, 3000]; % 原始ADC数据
%   rawData3 = [1200, 1800, 1600, 2800];
%   [processed2, processed3] = preprocessData(rawData2, rawData3);
%
%   % 检查预处理效果
%   fprintf('原始数据范围: [%.0f, %.0f]\n', min(rawData2), max(rawData2));
%   fprintf('预处理后范围: [%.3f, %.3f]\n', min(processed2), max(processed2));
%
%   注意事项:
%   - 输入数据应为数值向量，长度必须相同
%   - 归一化因子8192适用于14位ADC系统
%   - 去趋势处理会改变信号的绝对幅值
%   - 预处理后的信号适合进行频域分析和滤波
%
%   参见: DETREND, BANDPASS, CSV_LVBO_TO_MAT_TT
%
%   作者: [作者姓名]
%   日期: [创建日期]
%   版本: 1.0

    % 输入参数验证
    if nargin < 2
        error('preprocessData:NotEnoughInputs', '需要两个输入参数：column2和column3');
    end

    if length(column2) ~= length(column3)
        error('preprocessData:DimensionMismatch', '两个输入向量的长度必须相同');
    end

    % 确保输入为列向量
    column2 = column2(:);
    column3 = column3(:);



    % 步骤1: 消除直流分量 (去除信号均值)
    column2 = column2 - mean(column2);
    column3 = column3 - mean(column3);

    % 步骤2: 线性去趋势处理 (消除线性趋势)
    column2 = detrend(column2);
    column3 = detrend(column3);

    % 步骤3: 归一化处理 (14位ADC系统，8192为半量程)
    column2 = column2 / 8192;
    column3 = column3 / 8192;
    % column2=column2/max(abs(column2));
    % column3=column3/max(abs(column3));
    
    % 输出预处理结果
    processedColumn2 = column2;
    processedColumn3 = column3;
end